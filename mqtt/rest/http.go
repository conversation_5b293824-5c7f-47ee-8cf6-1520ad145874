package rest

import (
	"encoding/json"
	"net/http"
)

func Ok(w http.ResponseWriter, data any) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(data); err != nil {
		w.Write<PERSON>eader(http.StatusInternalServerError)
	}
}

func Error(w http.ResponseWriter, code int, err string) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	if e := json.NewEncoder(w).Encode(err); e != nil {
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusInternalServerError)
	}
}
