storage-way: 3  #Storage way optional items:0 memory、1 bolt、2 badger、3 redis;Only redis can be used in cluster mode.
bridge-way: 0  #Bridge way optional items:0 disable、1 kafka
bridge-path: ./config/bridge-kafka.yml  #The bridge config file path
pprof-enable: false #Whether to enable the performance analysis tool http://ip:6060

auth:
  way: 0  #Authentication way: 0 anonymous, 1 username and password, 2 clientid
  datasource: 1  #Optional items:0 free、1 redis、2 mysql、3 postgresql、4 http ...
  conf-path: ./config/auth-redis.yml  #The config file path should correspond to the auth-datasource
  blacklist-path: ./config/blacklist.yml  #Special rules outside the usual rules (black and white list)，this configuration is invalid for anonymous authentication

cluster:
  discovery-way: 0 #The node discovery way in the cluster: 0 serf、1 memberlist
  node-name: c02  #For versatility, use pure numbers. The name of node must be unique in the cluster.
  bind-addr: 127.0.0.1 #Configuration related to what address to bind to and ports to listen on.
  bind-port: 7947 #The port is used for both UDP and TCP gossip. Used for member discovery and join.
  advertise-addr: #Configuration related to what address to advertise to other cluster members. Used for nat traversal. The default value is bind-addr.
  advertise-port: #Used for member communication. If this port is not set, the default value is bind-port.
  members: [127.0.0.1:7946]  #Seeds member list, format such as *************:7946,*************:7946
  queue-depth: 10240 #Size of memberlist internal channel which handles UDP messages.
  raft-impl: 0 #The raft implementer: 0 hashicorp/raft, 1 etcd/raft
  raft-port: 8947 #Distributed consistency coordination communication port
  raft-dir: data/c02 #Distributed data storage directory
  raft-bootstrap: false  #Should be `true` for the first node of the cluster. It is required so that it can elect a leader without any other nodes being present.
  grpc-enable: true  #Grpc is used for raft transport and reliable communication between nodes
  grpc-port: 17947  #Grpc communication port between nodes
  inbound-pool-size: 40960 #The maximum number of goroutine to process incoming messages.
  outbound-pool-size: 40960 #The maximum number of goroutine to process outgoing messages.
  inout-pool-nonblocking: false #Pool size is unlimited, when inout-pool-nonblocking is true, inbound-pool-size and outbound-pool-size is inoperative.

mqtt:
  tcp: :1885
  ws: :1886
  http: :8081
  tls:
    ca-cert:   #CA root certificate file path. Not empty enable bidirectional authentication.
    server-cert:   #Server certificate file path
    server-key:   #server rsa private key file path
  options:
    client-write-buffer-size: 1024 #It is the number of individual workers and queues to initialize.
    client-read-buffer-size: 1024  #It is the size of the queue per worker.
    sys-topic-resend-interval: 1 #It specifies the interval between $SYS topic updates in seconds.
    inline-client: true #Whether to enable the inline client.
    capabilities:
      compatibilities:
        obscure-not-authorized: false #Return unspecified errors instead of not authorized
        passive-client-disconnect: false #Don't disconnect the client forcefully after sending disconnect packet (paho)
        always-return-response: false #Always return response info (useful for testing)
        restore-sys-info-restart: false #Restore system info from store as if server never stopped
      maximum-message-expiry-interval: 86400 #Maximum message expiry if message expiry is 0 or over
      maximum-session-expiry-interval: 4294967295 #Maximum number of seconds to keep disconnected sessions
      maximum-client-writes-pending: 65535 #Maximum number of pending message writes for a client
      maximum-packet-size: 0 #Maximum packet size, 0 unlimited
      receive-maximum: 1024 #Maximum number of concurrent qos messages per client
      topic-alias-maximum: 65535 #Maximum topic alias value
      maximum-qos: 2 #Maxmimum qos value available to clients
      retain-available: 1 #Retain messages is available
      wildcard-sub-available: 1 #Wildcard subscriptions are available
      sub-id-available: 1 #Subscription identifiers are available
      shared-sub-available: 1 #Shared subscriptions are available
      minimum-protocol-version: 3 #Minimum supported mqtt version (3.0.0)

redis:
  options:
    addr: 127.0.0.1:6379
    username:
    password:
    db: 0
  prefix: comqtt

log:
  enable: true #Indicates whether logging is enabled.
  format: 0 #Log format, currently supports Text: 0 and JSON: 1, with Text as the default.
  output: 2 #Log output location Console: 0 or File: 1 or Both: 2, with Console as the default.
  filename: ./logs/comqtt2.log #Filename is the file to write logs to
  maxsize: 100 #MaxSize is the maximum size in megabytes of the log file before it gets rotated. It defaults to 100 megabytes.
  max-age: 30 #MaxAge is the maximum number of days to retain old log files based on the timestamp encoded in their filename
  max-backups: 10 #MaxBackups is the maximum number of old log files to retain
  compress:  true #Compress determines if the rotated log files should be compressed using gzip
  level: 0 #Log level, with supported values LevelDebug: -4, LevelInfo: 0, LevelWarn: 4, and LevelError: 8.