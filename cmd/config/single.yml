storage-way: 1  #Storage way optional items:0 memory、1 bolt、2 badger、3 redis;Only redis can be used in cluster mode.
storage-path: comqtt.db  #Local storage path in single node mode.
bridge-way: 0  #Bridge way optional items:0 disable、1 kafka
bridge-path: ./config/bridge-kafka.yml  #The bridge config file path
pprof-enable: false #Whether to enable the performance analysis tool http://ip:6060

auth:
  way: 1  #Authentication way: 0 anonymous, 1 username and password, 2 clientid
  datasource: 4  #Optional items:0 free、1 redis、2 mysql、3 postgresql、4 http ...
  conf-path: ./config/auth-http.yml  #The config file path should correspond to the auth-datasource

mqtt:
  tcp: :1883
  ws: :1882
  http: :8080
  tls:
    ca-cert:   #CA root certificate file path. Not empty enable bidirectional authentication.
    server-cert:   #Server certificate file path
    server-key:   #server rsa private key file path
  options:
    client-write-buffer-size: 1024 #It is the number of individual workers and queues to initialize.
    client-read-buffer-size: 1024  #It is the size of the queue per worker.
    sys-topic-resend-interval: 10 #It specifies the interval between $SYS topic updates in seconds.
    inline-client: true #Whether to enable the inline client.
    capabilities:
      compatibilities:
        obscure-not-authorized: false #Return unspecified errors instead of not authorized
        passive-client-disconnect: false #Don't disconnect the client forcefully after sending disconnect packet (paho)
        always-return-response: false #Always return response info (useful for testing)
        restore-sys-info-restart: false #Restore system info from store as if server never stopped
      maximum-message-expiry-interval: 86400 #Maximum message expiry if message expiry is 0 or over
      maximum-session-expiry-interval: 4294967295 #Maximum number of seconds to keep disconnected sessions
      maximum-client-writes-pending: 65535 #Maximum number of pending message writes for a client
      maximum-packet-size: 0 #Maximum packet size, 0 unlimited
      receive-maximum: 1024 #Maximum number of concurrent qos messages per client
      topic-alias-maximum: 65535 #Maximum topic alias value
      maximum-qos: 2 #Maxmimum qos value available to clients
      retain-available: 1 #Retain messages is available
      wildcard-sub-available: 1 #Wildcard subscriptions are available
      sub-id-available: 1 #Subscription identifiers are available
      shared-sub-available: 1 #Shared subscriptions are available
      minimum-protocol-version: 3 #Minimum supported mqtt version (3.0.0)

redis:
  options:
    addr: 127.0.0.1:6379
    username:
    password:
    db: 0
  prefix: comqtt

log:
  enable: true #Indicates whether logging is enabled.
  format: 1 #Log format, currently supports Text: 0 and JSON: 1, with Text as the default.
  output: 2 #Log output location Console: 0 or File: 1 or Both: 2, with Console as the default.
  filename: ./logs/comqtt.log #Filename is the file to write logs to
  maxsize: 100 #MaxSize is the maximum size in megabytes of the log file before it gets rotated. It defaults to 100 megabytes.
  max-age: 30 #MaxAge is the maximum number of days to retain old log files based on the timestamp encoded in their filename
  max-backups: 10 #MaxBackups is the maximum number of old log files to retain
  compress:  true #Compress determines if the rotated log files should be compressed using gzip
  level: 0 #Log level, with supported values LevelDebug: -4, LevelInfo: 0, LevelWarn: 4, and LevelError: 8.