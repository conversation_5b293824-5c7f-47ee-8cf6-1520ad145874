name: Go package

on: [pull_request, push]

jobs:
  build:

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Build
        run: go build ./...

      - name: Docker compose up
        run: |
          docker compose up -d

          until docker exec mysql mysqladmin ping -h "127.0.0.1" --silent; do
            echo 'waiting for mysql...'
            sleep 3
          done

      - name: Test
        run: go test -v ./...
